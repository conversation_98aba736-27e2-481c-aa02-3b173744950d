import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/category_symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/buy_sell_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PriceDisplay extends StatelessWidget {
  const PriceDisplay({
    super.key,
    required this.symbol,
    this.onBuyTap,
    this.onSellTap,
  });
  final String symbol;
  final VoidCallback? onBuyTap;
  final VoidCallback? onSellTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<CategorySymbolsBloc, SymbolsState>(
      buildWhen:
          (previous, current) =>
              // Rebuild when price data is updated (state becomes SymbolsPriceSuccessState)
              current.currentState is SymbolsPriceSuccessState ||
              // Rebuild when view type changes
              previous.priceInfoViewType != current.priceInfoViewType ||
              // Rebuild when quote data reference changes (initial load)
              previous.symbolsQuote[symbol] != current.symbolsQuote[symbol],
      builder: (builderContext, state) {
        final symbolQuote = state.symbolsQuote[symbol];
        final viewType = state.priceInfoViewType;
        if (symbolQuote == null)
          return Platform.environment.containsKey('FLUTTER_TEST')
              ? Container(
                height: 100,
                width: 200,
                color: theme.background.bgBrandSecondary,
                child: Text('Golden test shimmer'),
              )
              : Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                  color: theme.utility.utilityGray200,
                ),
                width: 105,
                height: 33,
              );

        if (viewType == SymbolPriceInfoViewType.buySellPrice) {
          final duploTextStyles = context.duploTextStyles;
          return Stack(
            clipBehavior: Clip.none,
            alignment: AlignmentDirectional.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: BuySellContainer(
                      price: symbolQuote.getFormattedBid(
                        Localizations.localeOf(context).toString(),
                      ),
                      type: TradeType.sell,
                      onTap: onSellTap,
                    ),
                  ),
                  SizedBox(width: 4),
                  Expanded(
                    child: BuySellContainer(
                      type: TradeType.buy,
                      price: symbolQuote.getFormattedAsk(
                        Localizations.localeOf(context).toString(),
                      ),
                      onTap: onBuyTap,
                    ),
                  ),
                ],
              ),
              Positioned(
                top: -5,
                child: Container(
                  constraints: BoxConstraints(minWidth: 46, minHeight: 21),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 11,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.background.bgPrimary,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(4),
                    ),
                  ),
                  child: DuploText(
                    text: symbolQuote.getFormattedSpread(
                      Localizations.localeOf(context).toString(),
                    ),
                    style: duploTextStyles.textXxs,
                    color: theme.text.textPrimary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    fontWeight: DuploFontWeight.medium,
                  ),
                ),
              ),
            ],
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 12),
          child: SymbolPriceAndPercentageWidget(
            price: symbolQuote.midPrice,
            percentage: symbolQuote.dailyChange,
            digits: symbolQuote.digits,
          ),
        );
      },
    );
  }
}
