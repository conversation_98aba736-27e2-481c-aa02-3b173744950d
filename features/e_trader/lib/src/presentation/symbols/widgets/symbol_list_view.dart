import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/duplo/sticky_delegate.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/category_symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/services/symbol_subscription_batch_service.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_list_item.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class SymbolListView extends StatefulWidget {
  const SymbolListView({
    super.key,
    this.emptyBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.query,
    this.isSearchView = false,
    required this.tabController,
    required this.tabIndex,
    required this.tabSubscriptionManager,
  });
  final WidgetBuilder? emptyBuilder;
  final WidgetBuilder? errorBuilder;
  final WidgetBuilder? loadingBuilder;
  final String? query;
  final bool isSearchView;
  final TabController tabController;
  final int tabIndex;
  final TabSubscriptionManager tabSubscriptionManager;

  @override
  State<SymbolListView> createState() => _SymbolListViewState();
}

class _SymbolListViewState extends State<SymbolListView>
    with AutomaticKeepAliveClientMixin, RouteAwareAppLifecycleMixin {
  List<String> _currentVisibleSymbols = [];
  late ListVisibilityController<String> _visibilityController;

  @override
  void initState() {
    super.initState();
    _visibilityController = ListVisibilityController();
    _initializeTabSubscriptionManager();
  }

  String _getSymbolAtIndex(int index) {
    final state = context.read<CategorySymbolsBloc>().state;
    final symbolEntry = state.symbolsDetail.entries.elementAtOrNull(index);
    if (symbolEntry != null) {
      return symbolEntry.value.platformName;
    }
    return ''; // Fallback for invalid index
  }

  void _initializeTabSubscriptionManager() {
    // Register with TabSubscriptionManager for proper tab visibility handling
    widget.tabSubscriptionManager.registerTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _onTabSubscribe,
      onUnsubscribe: _onTabUnsubscribe,
    );
  }

  void _onTabSubscribe() {
    // Tab became active - subscribe to currently visible items
    diContainer<SymbolSubscriptionBatchService>().requestSubscriptions(
      _currentVisibleSymbols,
    );
  }

  void _onTabUnsubscribe() => diContainer<SymbolSubscriptionBatchService>()
      .requestUnsubscriptions(_currentVisibleSymbols);

  void _handleVisibilityChanged(List<String> visibleSymbols) {
    if (_shouldUpdateSubscriptions()) {
      diContainer<SymbolSubscriptionBatchService>().updateSubscriptions(
        _currentVisibleSymbols,
        visibleSymbols,
      );
    }

    _currentVisibleSymbols = List.of(visibleSymbols);
  }

  bool _shouldUpdateSubscriptions() =>
      widget.tabSubscriptionManager.isActive &&
      widget.tabSubscriptionManager.currentActiveTab == widget.tabIndex;

  void _handleSymbolSelection(
    SymbolDetailViewModel symbolDetail,
    TradeType? tradeType,
  ) {
    final selectedSymbolPlatformName = symbolDetail.platformName;
    _currentVisibleSymbols =
        _currentVisibleSymbols
            .where((symbol) => symbol != selectedSymbolPlatformName)
            .toList();

    if (context.mounted) {
      context.read<CategorySymbolsBloc>().add(
        SymbolsEvent.gotoDetails(
          symbolDetail: symbolDetail,
          tradeDirection: tradeType,
        ),
      );
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    if (_shouldUpdateSubscriptions()) {
      _onTabSubscribe();
      if (!_currentVisibleSymbols.deepEquals(
        _visibilityController.currentVisibleItems,
      )) {
        _currentVisibleSymbols = List.of(
          _visibilityController.currentVisibleItems,
        );
      }
    }
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    if (_shouldUpdateSubscriptions()) {
      _onTabUnsubscribe();
    }
  }

  @override
  void onAppForeground() {
    super.onAppForeground();
    if (_shouldUpdateSubscriptions()) {
      _onTabSubscribe();
    }
  }

  @override
  void onAppBackground() {
    super.onAppBackground();
    if (_shouldUpdateSubscriptions()) {
      _onTabUnsubscribe();
    }
  }

  @override
  void dispose() {
    widget.tabSubscriptionManager.unregisterTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _onTabSubscribe,
      onUnsubscribe: _onTabUnsubscribe,
    );
    _visibilityController.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocListener(
      listeners: [
        // Error handling listener
        BlocListener<CategorySymbolsBloc, SymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState &&
                  current.currentState is SymbolsErrorState,
          listener: (listenerContext, state) {
            if (state.currentState is SymbolsErrorState) {
              if (!Platform.environment.containsKey('FLUTTER_TEST')) {
                final toast = DuploToast();
                toast.hidesToastMessage();
                toast.showToastMessage(
                  autoCloseDuration: Duration.zero,
                  context: listenerContext,
                  widget: DuploToastMessage(
                    titleMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingError,
                    descriptionMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingErrorDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                    actionButtonTitle:
                        EquitiLocalization.of(listenerContext).trader_reload,
                    onTap: () {
                      toast.hidesToastMessage();
                      final symbolsBloc =
                          listenerContext.read<CategorySymbolsBloc>();
                      // final currentCategoryID =
                      //     symbolsBloc.state.selectedCategoryID;
                      toast.hidesToastMessage();

                      // Reset pagination and reload the current category
                      symbolsBloc.add(SymbolsEvent.onGetSymbols());
                    },
                  ),
                );
              }
            }
          },
        ),
        BlocListener<CategorySymbolsBloc, SymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState &&
                  (current.currentState is SymbolsSuccessState ||
                      current.currentState is SymbolsPriceSuccessState),
          listener: (listenerContext, state) {
            _visibilityController.updateItemCount(state.symbolsDetail.length);
          },
        ),
      ],
      child: ListVisibilityTracker<String>(
        itemHeight: kSymbolItemHeightWithDivider,
        itemBuilder: _getSymbolAtIndex,
        onVisibilityChanged: _handleVisibilityChanged,
        controller: _visibilityController,
        child: CustomScrollView(
          slivers: [
            BlocBuilder<CategorySymbolsBloc, SymbolsState>(
              buildWhen: (previous, current) {
                // Rebuild for structural changes but not pure price updates
                // Allow rebuilds for pagination and initial data loading

                // Only prevent rebuilds when it's a pure price update:
                // - Same number of symbols (no pagination)
                // - Same category
                // - Same view type
                // - Transition to SymbolsPriceSuccessState
                if (previous.currentState is! SymbolsPriceSuccessState &&
                    current.currentState is SymbolsPriceSuccessState &&
                    previous.symbolsDetail.length ==
                        current.symbolsDetail.length &&
                    previous.priceInfoViewType == current.priceInfoViewType) {
                  // This is a pure price update, don't rebuild
                  return false;
                }

                return previous.symbolsDetail.length !=
                        current.symbolsDetail.length ||
                    previous.currentState.runtimeType !=
                        current.currentState.runtimeType ||
                    previous.priceInfoViewType != current.priceInfoViewType;
              },
              builder: (builderContext, state) {
                final isSuccess = switch (state.currentState) {
                  SymbolsSuccessState() || SymbolsPriceSuccessState() => true,
                  _ => false,
                };
                final symbolsDetail =
                    isSuccess
                        ? state.symbolsDetail
                        : <String, SymbolDetailViewModel>{};

                if (state.currentState is SymbolsLoadingState) {
                  return SliverToBoxAdapter(
                    child: DuploShimmerList(
                      hasLeading: true,
                      hasTrailing: true,
                    ),
                  );
                }

                return PagedView.sliver(
                  physics:
                      (isSuccess && symbolsDetail.isEmpty) ||
                              state.currentState == SymbolsProcessState.error()
                          ? const NeverScrollableScrollPhysics()
                          : const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemCount: isSuccess ? state.symbolsDetail.length : 0,
                  centerError: true,
                  centerLoading: true,
                  centerEmpty: true,
                  hasError: switch (state.currentState) {
                    SymbolsErrorState() => true,
                    _ => false,
                  },
                  isLoading: switch (state.currentState) {
                    SymbolsLoadingState() => true,
                    SymbolschangeTabState() => state.symbolsDetail.isEmpty,
                    _ => false,
                  },
                  hasReachedMax: state.hasReachedMax,
                  emptyBuilder:
                      isSuccess && symbolsDetail.isEmpty
                          ? (widget.emptyBuilder ??
                              (ctx) => EmptyOrErrorSymbolsView(
                                message:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noSymbolsFound,
                                title:
                                    EquitiLocalization.of(
                                      context,
                                    ).trader_noMarkets,
                                image: trader.Assets.images.emptySearch.svg(),
                              ))
                          : null,
                  loadingBuilder:
                      widget.loadingBuilder ??
                      (ctx) =>
                          DuploShimmerList(hasLeading: true, hasTrailing: true),
                  errorBuilder:
                      widget.errorBuilder ??
                      (ctx) => EmptyOrErrorSymbolsView(
                        message:
                            EquitiLocalization.of(
                              context,
                            ).trader_marketsLoadFailed,
                        title:
                            EquitiLocalization.of(
                              context,
                            ).trader_somethingWentWrong,
                        image: trader.Assets.images.searchError.svg(),
                      ),
                  separatorBuilder:
                      (ctx, index) => Divider(
                        color: context.duploTheme.border.borderSecondary,
                        height: 1,
                      ),
                  onFetchData: () {
                    if (context.mounted)
                      context.read<CategorySymbolsBloc>().add(
                        SymbolsEvent.onGetSymbols(query: widget.query),
                      );
                  },
                  itemBuilder: (ctx, index) {
                    final symbolDetailMap = symbolsDetail.entries
                        .elementAtOrNull(index);
                    if (symbolDetailMap == null) {
                      return const SizedBox.shrink();
                    }
                    return SymbolListItem(
                      key: ValueKey(symbolDetailMap.key),
                      symbol: symbolDetailMap.value,
                      onTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            null,
                          ),
                    );
                  },
                );
              },
            ),
            BlocSelector<CategorySymbolsBloc, SymbolsState, bool>(
              selector: (state) => state.hasReachedMax,
              builder: (_, hasReachedMax) {
                if (hasReachedMax) {
                  return SliverToBoxAdapter();
                }
                return SliverPersistentHeader(
                  pinned: true,
                  floating: true,
                  delegate: StickyDelegate(
                    child: Center(child: CircularProgressIndicator.adaptive()),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
