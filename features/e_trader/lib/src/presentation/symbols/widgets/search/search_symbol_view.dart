import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/duplo/sticky_delegate.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/search_symbol/search_symbol_bloc.dart';
import 'package:e_trader/src/presentation/symbols/services/symbol_subscription_batch_service.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/search_symbol_item.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class SearchSymbolView extends StatefulWidget {
  const SearchSymbolView({
    super.key,
    this.emptyBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.query,
  });
  final WidgetBuilder? emptyBuilder;
  final WidgetBuilder? errorBuilder;
  final WidgetBuilder? loadingBuilder;
  final String? query;

  @override
  State<SearchSymbolView> createState() => _SearchSymbolViewState();
}

class _SearchSymbolViewState extends State<SearchSymbolView>
    with AutomaticKeepAliveClientMixin, RouteAwareAppLifecycleMixin {
  List<String> _currentVisibleSymbols = [];
  late ListVisibilityController<String> _visibilityController;

  @override
  void initState() {
    super.initState();
    _visibilityController = ListVisibilityController();
  }

  String _getSymbolAtIndex(int index) {
    final state = context.read<SearchSymbolBloc>().state;
    final symbolEntry = state.symbolsDetail.entries.elementAtOrNull(index);
    if (symbolEntry != null) {
      return symbolEntry.value.platformName;
    }
    return '';
  }

  void _handleVisibilityChanged(List<String> visibleSymbols) {
    diContainer<SymbolSubscriptionBatchService>().updateSubscriptions(
      _currentVisibleSymbols,
      visibleSymbols,
    );

    _currentVisibleSymbols = List.of(visibleSymbols);
  }

  void _handleSymbolSelection(
    SymbolDetailViewModel symbolDetail,
    TradeType? tradeType,
  ) {
    final selectedSymbolPlatformName = symbolDetail.platformName;
    _currentVisibleSymbols =
        _currentVisibleSymbols
            .where((symbol) => symbol != selectedSymbolPlatformName)
            .toList();

    if (context.mounted) {
      context.read<SearchSymbolBloc>().add(
        SearchSymbolEvent.gotoDetails(
          symbolDetail: symbolDetail,
          tradeDirection: tradeType,
        ),
      );
    }
  }

  @override
  void dispose() {
    _visibilityController.clear();
    super.dispose();
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    diContainer<SymbolSubscriptionBatchService>().requestSubscriptions(
      _currentVisibleSymbols,
    );
    if (!_currentVisibleSymbols.deepEquals(
      _visibilityController.currentVisibleItems,
    )) {
      _currentVisibleSymbols = List.of(
        _visibilityController.currentVisibleItems,
      );
    }
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    diContainer<SymbolSubscriptionBatchService>().requestUnsubscriptions(
      _currentVisibleSymbols,
    );
  }

  @override
  void onAppForeground() {
    super.onAppForeground();
    diContainer<SymbolSubscriptionBatchService>().requestSubscriptions(
      _currentVisibleSymbols,
    );
  }

  @override
  void onAppBackground() {
    super.onAppBackground();
    diContainer<SymbolSubscriptionBatchService>().requestUnsubscriptions(
      _currentVisibleSymbols,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      color: context.duploTheme.background.bgPrimary,
      child: BlocConsumer<SearchSymbolBloc, SearchSymbolState>(
        listenWhen: (previous, current) {
          if (previous.currentState is SymbolsPriceSuccessState &&
              current.currentState is SymbolsPriceSuccessState &&
              previous.symbolsDetail.length == current.symbolsDetail.length) {
            // This is a pure price update between two price states, don't listen
            return false;
          }
          return true;
        },
        listener: (listenerContext, state) {
          _visibilityController.updateItemCount(state.symbolsDetail.length);
        },
        buildWhen: (previous, current) {
          // Rebuild for structural changes but not pure price updates
          // Allow rebuilds for pagination and initial data loading

          // Only prevent rebuilds when it's a pure price update:
          // - Same number of symbols (no pagination)
          // - Same category
          // - Transition to SymbolsPriceSuccessState
          if (previous.currentState is! SymbolsPriceSuccessState &&
              current.currentState is SymbolsPriceSuccessState &&
              previous.symbolsDetail.length == current.symbolsDetail.length) {
            // This is a pure price update, don't rebuild
            return false;
          }

          return true;
        },
        builder: (ctx, state) {
          final symbolsDetail = state.symbolsDetail;
          final isSuccess =
              state.currentState is SymbolsSuccessState ||
              state.currentState is SymbolsPriceSuccessState;

          return ListVisibilityTracker<String>(
            itemHeight: kSearchSymbolItemHeightWithDivider,
            itemBuilder: _getSymbolAtIndex,
            onVisibilityChanged: _handleVisibilityChanged,
            controller: _visibilityController,
            child: CustomScrollView(
              slivers: [
                PagedView.sliver(
                  physics:
                      (isSuccess && symbolsDetail.isEmpty) ||
                              state.currentState == SymbolsProcessState.error()
                          ? const NeverScrollableScrollPhysics()
                          : const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemCount: isSuccess ? state.symbolsDetail.length : 0,
                  centerError: true,
                  centerLoading: true,
                  centerEmpty: true,
                  hasError: switch (state.currentState) {
                    SymbolsErrorState() => true,
                    _ => false,
                  },
                  isLoading: switch (state.currentState) {
                    SymbolsLoadingState() => true,
                    _ => false,
                  },
                  hasReachedMax: state.hasReachedMax,
                  emptyBuilder:
                      isSuccess && symbolsDetail.isEmpty
                          ? (widget.emptyBuilder ??
                              (buildContext) => EmptyOrErrorSymbolsView(
                                message:
                                    EquitiLocalization.of(
                                      buildContext,
                                    ).trader_noSymbolsFound,
                                title:
                                    EquitiLocalization.of(
                                      buildContext,
                                    ).trader_noMarkets,
                                image: trader.Assets.images.emptySearch.svg(),
                              ))
                          : null,
                  loadingBuilder:
                      widget.loadingBuilder ??
                      (buildContext) =>
                          DuploShimmerList(hasLeading: true, hasTrailing: true),
                  errorBuilder:
                      widget.errorBuilder ??
                      (buildContext) => EmptyOrErrorSymbolsView(
                        message:
                            EquitiLocalization.of(
                              buildContext,
                            ).trader_marketsLoadFailed,
                        title:
                            EquitiLocalization.of(
                              buildContext,
                            ).trader_somethingWentWrong,
                        image: trader.Assets.images.searchError.svg(),
                      ),
                  separatorBuilder:
                      (buildContext, index) => Divider(
                        color: buildContext.duploTheme.border.borderSecondary,
                        height: 1,
                      ),
                  onFetchData: () {
                    if (context.mounted && widget.query != null) {
                      // Use loadMoreSymbols for pagination instead of onSearchSymbols
                      context.read<SearchSymbolBloc>().add(
                        SearchSymbolEvent.loadMoreSymbols(),
                      );
                    }
                  },
                  itemBuilder: (buildContext, index) {
                    final symbolDetailMap = symbolsDetail.entries
                        .elementAtOrNull(index);
                    return SearchSymbolItem(
                      key: ValueKey(symbolDetailMap?.key),
                      symbol: symbolDetailMap!.value,
                      onTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            null,
                          ),
                      onBuyTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            TradeType.buy,
                          ),
                      onSellTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            TradeType.sell,
                          ),
                    );
                  },
                ),

                BlocSelector<SearchSymbolBloc, SearchSymbolState, bool>(
                  selector: (symstate) => symstate.hasReachedMax,
                  builder: (_, hasReachedMax) {
                    if (hasReachedMax) {
                      return SliverToBoxAdapter();
                    }
                    return SliverPersistentHeader(
                      pinned: true,
                      floating: true,
                      delegate: StickyDelegate(
                        child: Center(
                          child: CircularProgressIndicator.adaptive(),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
