import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/symbols/bloc/search_symbol/search_symbol_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/buy_sell_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SearchPriceDisplay extends StatelessWidget {
  const SearchPriceDisplay({
    super.key,
    required this.symbol,
    this.onBuyTap,
    this.onSellTap,
  });
  final String symbol;
  final VoidCallback? onBuyTap;
  final VoidCallback? onSellTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocBuilder<SearchSymbolBloc, SearchSymbolState>(
      buildWhen: (previous, current) {
        // Only rebuild if this specific symbol's quote has changed
        final previousQuote = previous.symbolsQuote[symbol];
        final currentQuote = current.symbolsQuote[symbol];

        if (previousQuote == null && currentQuote == null) return false;
        if (previousQuote == null || currentQuote == null) return true;

        // Check if any relevant price data has changed
        return previousQuote.ask != currentQuote.ask ||
            previousQuote.bid != currentQuote.bid ||
            previousQuote.midPrice != currentQuote.midPrice ||
            previousQuote.dailyChange != currentQuote.dailyChange ||
            previousQuote.direction != currentQuote.direction ||
            previous.priceInfoViewType != current.priceInfoViewType;
      },
      builder: (ctx, state) {
        final symbolQuote = state.symbolsQuote[symbol];
        if (symbolQuote == null) {
          return SizedBox(
            width: 180,
            height: 47,
            child: DuploShimmerList(
              hasLeading: false,
              hasTrailing: false,
              itemCount: 1,
            ),
          );
        }

        if (state.priceInfoViewType == SymbolPriceInfoViewType.buySellPrice) {
          final duploTextStyles = ctx.duploTextStyles;
          return Stack(
            clipBehavior: Clip.none,
            alignment: AlignmentDirectional.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child:
                        onSellTap == null
                            ? BuySellContainer(
                              price: symbolQuote.getFormattedBid(
                                Localizations.localeOf(ctx).toString(),
                              ),
                              type: TradeType.sell,
                            )
                            : RepaintBoundary(
                              child: BuySellContainer(
                                price: symbolQuote.getFormattedBid(
                                  Localizations.localeOf(ctx).toString(),
                                ),
                                type: TradeType.sell,
                                onTap: onSellTap,
                              ),
                            ),
                  ),
                  SizedBox(width: 4),
                  Expanded(
                    child:
                        onBuyTap == null
                            ? BuySellContainer(
                              type: TradeType.buy,
                              price: symbolQuote.getFormattedAsk(
                                Localizations.localeOf(ctx).toString(),
                              ),
                            )
                            : RepaintBoundary(
                              child: BuySellContainer(
                                type: TradeType.buy,
                                price: symbolQuote.getFormattedAsk(
                                  Localizations.localeOf(ctx).toString(),
                                ),
                                onTap: onBuyTap,
                              ),
                            ),
                  ),
                ],
              ),
              Positioned(
                top: -5,
                child: Container(
                  constraints: BoxConstraints(minWidth: 46, minHeight: 21),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 11,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.background.bgPrimary,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(4),
                    ),
                  ),
                  child: DuploText(
                    text: symbolQuote.getFormattedSpread(
                      Localizations.localeOf(ctx).toString(),
                    ),
                    textAlign: TextAlign.center,
                    style: duploTextStyles.textXxs,
                    color: theme.text.textPrimary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    fontWeight: DuploFontWeight.medium,
                  ),
                ),
              ),
            ],
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 12),
          child: SymbolPriceAndPercentageWidget(
            price: symbolQuote.midPrice,
            percentage: symbolQuote.dailyChange,
            digits: symbolQuote.digits,
          ),
        );
      },
    );
  }
}
