import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/search_price_display.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:flutter/material.dart';

/// Height of a single search symbol item in pixels
const double kSearchSymbolItemHeight = 72.0;

/// Total height including divider (item height + 1px divider)
const double kSearchSymbolItemHeightWithDivider = 73.0;

class SearchSymbolItem extends StatelessWidget {
  const SearchSymbolItem({
    super.key,
    required this.symbol,
    required this.onTap,
    this.onBuyTap,
    this.onSellTap,
  });

  final SymbolDetailViewModel symbol;
  final VoidCallback onTap;
  final VoidCallback? onBuyTap;
  final VoidCallback? onSellTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        color: context.duploTheme.background.bgPrimary,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SizedBox(
            height: kSearchSymbolItemHeight,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(flex: 2, child: SymbolInfoWidget(symbol: symbol)),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 180, minHeight: 47),
                  child: RepaintBoundary(
                    child: SearchPriceDisplay(
                      symbol: symbol.platformName,
                      key: ValueKey(symbol.platformName),
                      onBuyTap: onBuyTap,
                      onSellTap: onSellTap,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
