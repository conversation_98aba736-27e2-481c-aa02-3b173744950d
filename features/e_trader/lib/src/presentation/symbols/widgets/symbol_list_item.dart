// ignore_for_file: prefer-number-format

import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/widgets/price_display.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:flutter/material.dart';

/// Height of a single symbol list item in pixels
const double kSymbolItemHeight = 72.0;

/// Total height including divider (item height + 1px divider)
const double kSymbolItemHeightWithDivider = 73.0;

class SymbolListItem extends StatelessWidget {
  const SymbolListItem({
    super.key,
    required this.symbol,
    this.onTap,
    this.onBuyTap,
    this.onSellTap,
  });

  final SymbolDetailViewModel symbol;
  final VoidCallback? onTap;
  final VoidCallback? onBuyTap;
  final VoidCallback? onSellTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        height: kSymbolItemHeight,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: onTap,
                child: SymbolInfoWidget(symbol: symbol),
              ),
            ),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 180, minHeight: 47),
              child: RepaintBoundary(
                child: PriceDisplay(
                  symbol: symbol.platformName,
                  onBuyTap: () {
                    onBuyTap?.call();
                    if (onBuyTap != null) onBuyTap!();
                  },
                  onSellTap: () {
                    if (onSellTap != null) onSellTap!();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
