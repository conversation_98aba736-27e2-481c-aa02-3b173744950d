import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/buy_sell/buy_sell_buttons.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_screen.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/news_tab.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/orders/orders_tab.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/trades_tab.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_widget.dart';
import 'package:e_trader/src/presentation/product_detail_overview/widget/product_detail_overview_screen.dart';
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:e_trader/src/presentation/trading_chart/advance_trading_view.dart';
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/watchlisted_symbol_indicator_screen.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:theme_manager/theme_manager.dart';

class ProductDetailsWidget extends StatefulWidget {
  final SymbolDetailViewModel symbolDetail;
  final String accountNumber;
  final TradeType? tradeDirection;

  const ProductDetailsWidget({
    super.key,
    required this.symbolDetail,
    required this.accountNumber,
    this.tradeDirection,
  });

  @override
  State<ProductDetailsWidget> createState() => _ProductDetailsWidgetState();
}

class _ProductDetailsWidgetState extends State<ProductDetailsWidget>
    with SingleTickerProviderStateMixin, PerformanceObserverMixin {
  late final SheetController _sheetController;
  late final SheetOffsetDrivenAnimation _sheetAnimation;
  late final TabController _tabController;
  late final TabSubscriptionManager _tabSubscriptionManager;
  bool _isSheetExpanded = false;
  double _snapPoint = 100;

  @override
  void onRoutePopped(Route<Object?> poppedRoute) {
    super.onRoutePopped(poppedRoute);
    if (_tabController.index == 0 &&
        poppedRoute.settings.name == 'price_alert_sheet') {
      _onSheetExpansionChanged(false);
    }
  }

  @override
  void onRoutePushed(Route<Object?> pushedRoute) {
    super.onRoutePushed(pushedRoute);
    if (_tabController.index == 0 &&
        pushedRoute.settings.name == 'price_alert_sheet') {
      _onSheetExpansionChanged(true);
    }
  }

  @override
  void initState() {
    super.initState();
    _sheetController = SheetController();
    _tabSubscriptionManager = TabSubscriptionManager();
    _tabController = TabController(
      length: 6,
      vsync: this,
      animationDuration: Duration.zero,
    );
    _sheetAnimation = SheetOffsetDrivenAnimation(
      controller: _sheetController,
      initialValue: 0.0, // Start hidden when collapsed
    );

    // Listen to tab changes and delegate to subscription manager
    _tabController.addListener(_handleTabChange);

    // Set initial active tab
    _tabSubscriptionManager.setInitialActiveTab(_tabController.index);
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _tabSubscriptionManager.handleTabChange(_tabController.index);
    }
  }

  void _onSheetExpansionChanged(bool isExpanded) {
    if (_isSheetExpanded != isExpanded) {
      setState(() {
        _isSheetExpanded = isExpanded;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _sheetController.dispose();
    _tabController.dispose();
    _tabSubscriptionManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final theme = context.duploTheme;

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) =>
                  diContainer<ProductDetailsBloc>()..add(
                    ProductDetailsEvent.fetchSymbolInfo(
                      widget.symbolDetail.platformName,
                    ),
                  ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<PositionBloc>(
                param1: widget.symbolDetail.platformName,
              ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<OrdersBloc>(
                param1: widget.symbolDetail.platformName,
              ),
        ),
        BlocProvider(
          create:
              (_) => diContainer<NewsBloc>(
                param1: widget.symbolDetail.platformName,
              )..add(NewsEvent.fetchNews()),
        ),
        BlocProvider(
          create:
              (_) => diContainer<EventsBloc>(
                param1: widget.symbolDetail.platformName,
              )..add(EventsEvent.fetchEvents()),
        ),
        BlocProvider(
          create:
              (_) => diContainer<CreateTradeBloc>(
                param1: (
                  digits: widget.symbolDetail.digit ?? 2,
                  symbolCode: widget.symbolDetail.platformName,
                  symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  assetType: widget.symbolDetail.assetType ?? "",
                  minLot: widget.symbolDetail.minLot,
                  maxLot: widget.symbolDetail.maxLot,
                  isForex: widget.symbolDetail.isForex,
                  lotsSteps: widget.symbolDetail.lotsSteps,
                ),
              )..add(
                CreateTradeEvent.subscribe(
                  orderSize: widget.symbolDetail.minLot,
                  eventType: TradingSocketEvent.marginRequirements.register,
                ),
              ),
        ),
      ],
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        resizeToAvoidBottomInset: false,
        appBar: DuploAppBar(
          title: '',
          duploAppBarTextAlign: DuploAppBarTextAlign.left,
          titleWidget: SymbolInfoWidget(symbol: widget.symbolDetail),
          titleSpacing: 0.0,
          actions: [
            DuploIconButton.customColorMode(
              onTap: () {
                DuploSheet.showModalSheetV2<void>(
                  context,
                  settings: RouteSettings(name: 'price_alert_sheet'),
                  appBar: DuploAppBar(
                    title: l10n.trader_priceAlert,
                    automaticallyImplyLeading: false,
                    duploAppBarTextAlign: DuploAppBarTextAlign.left,
                    actions: [
                      IconButton(
                        icon:
                            diContainer<ThemeManager>().isDarkMode
                                ? Assets.images.closeIc.svg(
                                  colorFilter: ColorFilter.mode(
                                    DuploTheme.of(
                                      context,
                                    ).foreground.fgSecondary,
                                    BlendMode.srcIn,
                                  ),
                                )
                                : Assets.images.closeIc.svg(),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  bottomBar: Container(height: 0),
                  content: PriceAlertWidget(
                    platformName: widget.symbolDetail.platformName,
                    symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                  ),
                );
              },
              colorMode: ColorMode.dynamicMode,
              icon: trader.Assets.images.priceAlertClock.keyName,
            ),
            const SizedBox(width: 8),
            WatchlistedSymbolIndicatorScreen(
              platformName: widget.symbolDetail.platformName,
              colorMode: ColorMode.dynamicMode,
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: Container(
          color:
              _tabController.index == 0
                  ? diContainer<ThemeManager>().isDarkMode
                      ? Color(0xFF131722)
                      : Color(0xFFFFFFFF)
                  : theme.background.bgSecondary,
          child: SafeArea(
            child: LayoutBuilder(
              builder: (layoutBuilderContext, constraints) {
                final tabHeight = constraints.maxHeight - _snapPoint;
                return Stack(
                  children: [
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      height: tabHeight,
                      child: ColoredBox(
                        color: theme.background.bgSecondary,
                        child: DuploTabBar(
                          tabController: _tabController,
                          tabTitles: [
                            DuploTabBarTitle(
                              text: l10n.trader_chart,
                              semanticsIdentifier: 'chart_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_portfolio_trades,
                              semanticsIdentifier: 'trades_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_orders,
                              semanticsIdentifier: 'orders_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_news,
                              semanticsIdentifier: 'news_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_events,
                              semanticsIdentifier: 'events_tab',
                            ),
                            DuploTabBarTitle(
                              text: l10n.trader_details,
                              semanticsIdentifier: 'details_tab',
                            ),
                          ],
                          isScrollable: true,
                          tabViews: [
                            AnimatedBuilder(
                              animation: _sheetAnimation,
                              builder: (animationContext, child) {
                                // Get the current sheet expansion progress (0.0 = collapsed, 0.98 = expanded)
                                final expansionProgress = _sheetAnimation.value;

                                // Start showing chart when sheet is 90% collapsed (10% down from full expansion)
                                // Create opacity that goes from 0.0 at 90% expansion to 0.98 at full collapse
                                final opacity =
                                    expansionProgress <= 0.9
                                        ? (0.98 - (expansionProgress / 0.9))
                                            .clamp(0.0, 0.98)
                                        : 0.0;

                                return _ChartContainer(
                                  shouldShow: opacity > 0.0,
                                  opacity: opacity,
                                  isSheetExpanded: _isSheetExpanded,
                                  symbolDetail: widget.symbolDetail,
                                  sheetController: _sheetController,
                                  tabController: _tabController,
                                );
                              },
                            ),
                            TradesTab(
                              platformName: widget.symbolDetail.platformName,
                              tabController: _tabController,
                              tabIndex: 1,
                              tabSubscriptionManager: _tabSubscriptionManager,
                            ),
                            OrdersTab(
                              platformName: widget.symbolDetail.platformName,
                              tabController: _tabController,
                              tabIndex: 2,
                              tabSubscriptionManager: _tabSubscriptionManager,
                            ),
                            NewsTab(isInMarketDetails: true),
                            EventsScreen(isInMarketDetails: true),
                            ProductDetailOverviewScreen(
                              platformName: widget.symbolDetail.platformName,
                              accountNumber: widget.accountNumber,
                              digit: widget.symbolDetail.digit ?? 5,
                            ),
                          ],
                          isFlex: false,
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: SheetViewport(
                        child: Sheet(
                          dragConfiguration: SheetDragConfiguration(
                            hitTestBehavior: HitTestBehavior.deferToChild,
                          ),
                          scrollConfiguration: SheetScrollConfiguration(),
                          controller: _sheetController,
                          decoration: MaterialSheetDecoration(
                            size: SheetSize.fit,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(24),
                            ),
                            color: theme.background.bgPrimary,
                          ),
                          physics: BouncingSheetPhysics(
                            behavior: DirectionAwareBouncingBehavior(
                              upward: SheetOffset(0),
                            ),
                          ),
                          initialOffset: SheetOffset.absolute(_snapPoint),
                          snapGrid: SheetSnapGrid(
                            snaps: [
                              SheetOffset.absolute(_snapPoint),
                              SheetOffset(0.98), // Full height
                            ],
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.border.borderSecondary,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                            ),
                            child: Column(
                              children: [
                                DraggableHandle(),
                                const SizedBox(height: 12),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: 16.0,
                                      right: 16,
                                      bottom: 16,
                                    ),
                                    child: BlocBuilder<
                                      ProductDetailsBloc,
                                      ProductDetailsState
                                    >(
                                      buildWhen:
                                          (previous, current) =>
                                              previous != current,
                                      builder: (blocBuilderContext, state) {
                                        final info = switch (state) {
                                          ProductDetailsSuccess(
                                            infoModel: final successInfo,
                                          ) =>
                                            successInfo,
                                          _ => null,
                                        };
                                        return _PlaceTradeSheetWidget(
                                          sheetController: _sheetController,
                                          symbolQuoteModel: info,
                                          symbolDetail: widget.symbolDetail,
                                          onSheetExpansionChanged:
                                              _onSheetExpansionChanged,
                                          initialTradeDirection:
                                              widget.tradeDirection,
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _PlaceTradeSheetWidget extends StatefulWidget {
  const _PlaceTradeSheetWidget({
    required SheetController sheetController,
    required this.symbolQuoteModel,
    required this.symbolDetail,
    required this.onSheetExpansionChanged,
    this.initialTradeDirection,
  }) : _sheetController = sheetController;

  final SheetController _sheetController;
  final SymbolQuoteModel? symbolQuoteModel;
  final SymbolDetailViewModel symbolDetail;
  final void Function(bool isExpanded) onSheetExpansionChanged;
  final TradeType? initialTradeDirection;

  @override
  State<_PlaceTradeSheetWidget> createState() => _PlaceTradeSheetWidgetState();
}

class _PlaceTradeSheetWidgetState extends State<_PlaceTradeSheetWidget>
    with RouteAwareAppLifecycleMixin {
  late final SheetOffsetDrivenAnimation _sheetAnimation;
  bool _isExpanded = false;
  double _previousOffset = 0.0;
  TradeType? _userSelectedTradeType; // Track user's actual selection
  bool _hasUserMadeSelection = false; // Track if user has made any selection

  @override
  void initState() {
    super.initState();
    _sheetAnimation = SheetOffsetDrivenAnimation(
      controller: widget._sheetController,
      initialValue: 0.0, // Start hidden when collapsed
    );
    _sheetAnimation.addListener(_sheetAnimationListener);
  }

  void _sheetAnimationListener() {
    final currentOffset = widget._sheetController.metrics?.offset ?? 0.0;
    final minOffset = widget._sheetController.metrics?.minOffset ?? 0.0;
    final maxOffset = widget._sheetController.metrics?.maxOffset ?? 0.98;

    // Calculate the threshold as a percentage between min and max
    // Use 95% threshold for both expansion and collapse
    const thresholdPercentage = 0.9;
    final threshold = maxOffset - (maxOffset - minOffset) * thresholdPercentage;

    // Determine if sheet is expanding (moving up) or collapsing (moving down)
    final isExpanding = currentOffset > _previousOffset;
    final isCollapsing = currentOffset < _previousOffset;

    // Disable clicks when sheet crosses 95% threshold during expansion
    if (currentOffset >= threshold && isExpanding && !_isExpanded) {
      setState(() {
        _isExpanded = true;
      });
      // Notify parent about expansion state change
      widget.onSheetExpansionChanged(true);
    }

    // Re-enable clicks and reset trade selection when sheet crosses 95% threshold during collapse
    if (currentOffset <= threshold && isCollapsing && _isExpanded) {
      if (context.read<CreateTradeBloc>().state.tradeType != null) {
        // Preserve user selection, or fall back to initial trade direction
        if (_hasUserMadeSelection && _userSelectedTradeType != null) {
          // User has made a selection, preserve it
          context.read<CreateTradeBloc>().add(
            CreateTradeEvent.updateTradeType(_userSelectedTradeType!),
          );
        } else if (widget.initialTradeDirection != null) {
          // No user selection, but we have initial trade direction
          context.read<CreateTradeBloc>().add(
            CreateTradeEvent.updateTradeType(widget.initialTradeDirection!),
          );
        } else {
          // No user selection and no initial trade direction, reset completely
          context.read<CreateTradeBloc>().add(
            CreateTradeEvent.resetTradeSelection(),
          );
        }
      }
      setState(() {
        _isExpanded = false;
      });
      // Notify parent about expansion state change
      widget.onSheetExpansionChanged(false);
    }

    // Update previous offset for next comparison
    _previousOffset = currentOffset;
  }

  @override
  dispose() {
    _sheetAnimation.removeListener(_sheetAnimationListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<CreateTradeBloc, CreateTradeState>(
      buildWhen: (previous, current) => previous != current,
      builder:
          (builderContext, createTradeState) => Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(24),
              ),
            ),
            //
            child: SheetContentScaffold(
              backgroundColor: theme.background.bgPrimary,
              topBar: AnimatedBuilder(
                animation: _sheetAnimation,
                builder: (animatedBuilderContext, child) {
                  return BuySellButtons(
                    onTap: (tradeType) {
                      // Track user selection
                      _userSelectedTradeType = tradeType;
                      _hasUserMadeSelection = true;

                      widget._sheetController.animateTo(
                        SheetOffset.absolute(
                          widget._sheetController.metrics?.maxOffset ?? 0.98,
                        ),
                        curve: Curves.fastOutSlowIn,
                      );

                      // Always update the trade type when user taps
                      animatedBuilderContext.read<CreateTradeBloc>().add(
                        CreateTradeEvent.updateTradeType(tradeType),
                      );
                    },
                    spread: widget.symbolQuoteModel?.spread ?? 0,
                    digits: widget.symbolQuoteModel?.digits ?? 2,
                    buyButtonState: _getBuyButtonState(createTradeState),
                    sellButtonState: _getSellButtonState(createTradeState),
                  );
                },
              ),
              body: AnimatedBuilder(
                animation: _sheetAnimation,
                child: CreateTradeWidget(
                  key: ValueKey(_isExpanded),
                  args: (
                    digits: widget.symbolQuoteModel?.digits ?? 2,
                    symbolCode: widget.symbolDetail.platformName,
                    symbolImageUrl: widget.symbolDetail.imageURL ?? "",
                    assetType: widget.symbolDetail.assetType ?? "",
                    minLot: widget.symbolDetail.minLot,
                    maxLot: widget.symbolDetail.maxLot,
                    isForex: widget.symbolDetail.isForex,
                    lotsSteps: widget.symbolDetail.lotsSteps,
                  ),
                ),
                builder: (animationContext, child) {
                  return FadeTransition(opacity: _sheetAnimation, child: child);
                },
              ),
            ),
          ),
    );
  }

  BuySellButtonState _getBuyButtonState(CreateTradeState createTradeState) {
    final askPrice = widget.symbolQuoteModel?.ask ?? 0.0;

    // If there's a current trade state from the bloc, use it
    final currentBuyState = createTradeState.buyButtonState();
    if (currentBuyState != null) {
      return currentBuyState;
    }

    // If bloc has no trade type set, determine based on our logic
    if (createTradeState.tradeType == null) {
      // Check user selection first
      if (_hasUserMadeSelection && _userSelectedTradeType == TradeType.buy) {
        return BuySellButtonState.selected(askPrice);
      }

      // If no user selection, check initial trade direction
      if (!_hasUserMadeSelection &&
          widget.initialTradeDirection == TradeType.buy) {
        return BuySellButtonState.selected(askPrice);
      }
    }

    // Default to active state
    return BuySellButtonState.active(askPrice);
  }

  BuySellButtonState _getSellButtonState(CreateTradeState createTradeState) {
    final bidPrice = widget.symbolQuoteModel?.bid ?? 0.0;

    // If there's a current trade state from the bloc, use it
    final currentSellState = createTradeState.sellButtonState();
    if (currentSellState != null) {
      return currentSellState;
    }

    // If bloc has no trade type set, determine based on our logic
    if (createTradeState.tradeType == null) {
      // Check user selection first
      if (_hasUserMadeSelection && _userSelectedTradeType == TradeType.sell) {
        return BuySellButtonState.selected(bidPrice);
      }

      // If no user selection, check initial trade direction
      if (!_hasUserMadeSelection &&
          widget.initialTradeDirection == TradeType.sell) {
        return BuySellButtonState.selected(bidPrice);
      }
    }

    // Default to active state
    return BuySellButtonState.active(bidPrice);
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    if (route.settings.name == 'confirmation_sheet') {
      widget._sheetController.animateTo(
        SheetOffset.absolute(widget._sheetController.metrics?.minOffset ?? 0.0),
        curve: Curves.fastOutSlowIn,
      );
    }
  }
}

class _ChartContainer extends StatelessWidget {
  final bool shouldShow;
  final double opacity;
  final bool isSheetExpanded;
  final SymbolDetailViewModel symbolDetail;
  final SheetController sheetController;
  final TabController tabController;

  const _ChartContainer({
    required this.shouldShow,
    required this.opacity,
    required this.isSheetExpanded,
    required this.symbolDetail,
    required this.sheetController,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Always keep chart in widget tree but position it offscreen when hidden
        Positioned(
          left: shouldShow ? 0 : -10000, // Move offscreen when hidden
          top: 0,
          right: shouldShow ? 0 : null,
          bottom: shouldShow ? 0 : null,
          width: shouldShow ? null : 1, // Minimal width when hidden
          height: shouldShow ? null : 1, // Minimal height when hidden
          child: AdvanceTradingView(
            platformName: symbolDetail.platformName,
            tickerName: symbolDetail.symbolName,
            digit: symbolDetail.digit ?? 5,
            sheetController: sheetController,
            interactionsEnabled: !isSheetExpanded,
            tabController: tabController,
            tabIndex: 0, // Chart tab is at index 0
          ),
        ),
      ],
    );
  }
}
