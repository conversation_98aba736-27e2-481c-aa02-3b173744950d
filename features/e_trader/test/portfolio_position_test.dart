// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'portfolio_position_screen_test_data.dart';
import 'scenarios/portfolio_insights_empty_scenario.dart';
import 'scenarios/portfolio_insights_failure_scenario.dart';
import 'scenarios/portfolio_insights_success_scenario.dart';
import 'scenarios/portfolio_position_success_scenario.dart';
import 'scenarios/portfolio_position_error_scenario.dart';
import 'scenarios/portfolio_position_empty_scenario.dart';
import 'scenarios/prtofolio_orders_success_scenario.dart';
import 'scenarios/portfolio_orders_empty_scenario.dart';
import 'scenarios/portfolio_orders_failure_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/i_wait_for_seconds.dart';
import './step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Portfolio Position Management''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: Verify Portfolio Position screen golden tests (scenarios: [portfolioPositionSuccessScenario], 'portfolio_position_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Verify Portfolio Position screen golden tests (scenarios: [portfolioPositionSuccessScenario], 'portfolio_position_success')''',
          );
          await theAppIsRendered(
            tester,
            PortfolioPositionScreenTestWrapper(),
            scenarios: [portfolioPositionSuccessScenario],
          );
          await screenshotVerified(tester, 'portfolio_position_success');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Verify Portfolio Position screen golden tests (scenarios: [portfolioPositionSuccessScenario], 'portfolio_position_success')''',
            success,
          );
        }
      },
    );
    testGoldens('''Success order tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Success order tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [
            portfolioPositionSuccessScenario,
            portfolioOrdersSuccessScenario,
          ],
        );
        await iTapIdentifier(tester, 'orders_tab');
        await iWait(tester);
        await screenshotVerified(tester, 'portfolio_Success_orders');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Success order tab''', success);
      }
    });
    testGoldens('''Empty order tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Empty order tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [
            portfolioPositionSuccessScenario,
            portfolioOrdersEmptyScenario,
          ],
        );
        await iTapIdentifier(tester, 'orders_tab');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'portfolio_empty_orders');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Empty order tab''', success);
      }
    });
    testGoldens('''Error order tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Error order tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [
            portfolioPositionSuccessScenario,
            portfolioOrdersFailureScenario,
          ],
        );
        await iTapIdentifier(tester, 'orders_tab');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'portfolio_error_orders');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Error order tab''', success);
      }
    });
    testGoldens('''Success insights tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Success insights tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [portfolioInsightsSuccessScenario],
        );
        await iTapIdentifier(tester, 'margin_tab');
        await screenshotVerifiedWithCustomPump(
          tester,
          'portfolio_insights_success',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Success insights tab''', success);
      }
    });
    testGoldens('''Failure insights tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Failure insights tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [portfolioInsightsFailureScenario],
        );
        await iTapIdentifier(tester, 'margin_tab');
        await iWait(tester);
        await screenshotVerified(tester, 'portfolio_insights_failure');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Failure insights tab''', success);
      }
    });
    testGoldens('''Empty insights tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Empty insights tab''');
        await theAppIsRendered(
          tester,
          PortfolioPositionScreenTestWrapper(),
          scenarios: [portfolioInsightsEmptyScenario],
        );
        await iTapIdentifier(tester, 'margin_tab');
        await screenshotVerifiedWithCustomPump(
          tester,
          'portfolio_insights_empty',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Empty insights tab''', success);
      }
    });
  });
}
